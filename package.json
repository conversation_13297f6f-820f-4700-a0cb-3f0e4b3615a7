{"name": "ax-dms", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,vue,css,scss,json}\""}, "dependencies": {"@primeuix/themes": "^1.0.2", "@vue-office/excel": "^1.7.14", "axios": "^1.5.0", "docx-preview": "^0.3.5", "jsmind": "^0.8.7", "marked": "^15.0.12", "pdfjs-dist": "^5.2.133", "pinia": "^2.1.6", "primeflex": "^3.3.1", "primeicons": "^6.0.1", "primevue": "^4.3.3", "vue": "^3.3.4", "vue-router": "^4.2.4", "xlsx": "^0.18.5"}, "browserslist": ["Android >= 4.1", "iOS >= 7.1", "Chrome > 31", "Firefox > 31", "IE >= 8", "last 2 versions"], "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@tailwindcss/postcss": "^4.1.3", "@vitejs/plugin-vue": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "postcss": "^8.5.3", "prettier": "^2.8.8", "prettier-plugin-stylus-supremacy": "^1.0.4", "prettier-plugin-tailwindcss": "^0.4.1", "stylus": "^0.63.0", "stylus-loader": "^8.1.0", "tailwindcss": "^4.1.3", "vite": "^4.4.9"}}