<template>
  <Dialog
    :visible="visible"
    :header="document?.title || '文档预览'"
    :style="{ width: '90vw', height: '90vh' }"
    :contentStyle="{ flex: 1 }"
    :modal="true"
    :closable="true"
    :draggable="false"
    :resizable="true"
    class="document-preview-dialog"
    @update:visible="handleVisibleChange"
    @hide="handleClose"
  >
    <!-- 文档预览标题 -->
    <template #header>
      <!-- 文档信息头部 -->
      <div v-if="document" class="document-info">
        <div class="info-item">
          <span class="label">文件名：</span>
          <span class="value">{{ document.title }}</span>
        </div>
        <div class="info-item">
          <span class="label">类型：</span>
          <Tag :value="document.type" />
        </div>
        <div class="info-item">
          <span class="label">大小：</span>
          <span class="value">{{ formatFileSize(document.size) }}</span>
        </div>
        <div class="info-item">
          <span class="label">上传时间：</span>
          <span class="value">{{ formatDateLocale(document.uploadTime) }}</span>
        </div>
      </div>
      <div v-else class="align-items-center flex gap-2">
        <span>{{ document?.title || '文档预览' }}</span>
        <Tag severity="info" value="文档预览" class="text-sm" />
      </div>
    </template>
    <div class="preview-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="4" />
        <p class="mt-3">正在加载文档...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <i class="pi pi-exclamation-triangle" style="font-size: 3rem; color: #e74c3c"></i>
        <p class="mt-3">{{ error }}</p>
        <Button label="重试" icon="pi pi-refresh" @click="() => {}" class="mt-3" />
      </div>

      <!-- 文档预览内容 -->
      <div v-else-if="document" class="preview-content">
        <!-- 预览内容区域 -->
        <div class="preview-area">
          <!-- 根据文件类型选择预览方式 -->
          <div class="document-preview">
            <!-- PDF 文件预览 -->
            <object v-if="documentUrl && isPdfFile" :data="documentUrl" type="application/pdf" width="100%" height="100%" style="border: none">
              <div class="pdf-fallback">
                <p>您的浏览器不支持 PDF 预览</p>
                <p class="text-sm text-gray-500">请使用下方的下载按钮获取文件</p>
              </div>
            </object>

            <!-- Word 文档预览 -->
            <div v-else-if="documentUrl && isWordFile" class="word-preview" ref="wordContainer">
              <div v-if="wordError" class="word-fallback">
                <p>Word 文档预览失败</p>
                <p class="text-sm text-gray-500">{{ wordError }}</p>
                <Button label="重试" icon="pi pi-refresh" @click="loadWordDocument" class="mt-3" />
              </div>
            </div>

            <!-- Excel 文件预览 -->
            <div v-else-if="documentUrl && isExcelFile" class="excel-preview">
              <VueOfficeExcel
                v-if="!excelError"
                :src="documentUrl"
                style="height: 100%; width: 100%;"
                @error="handleExcelError"
              />
              <div v-else class="excel-fallback">
                <p>Excel 文档预览失败</p>
                <p class="text-sm text-gray-500">{{ excelError }}</p>
                <Button label="重试" icon="pi pi-refresh" @click="loadExcelDocument" class="mt-3" />
              </div>
            </div>

            <!-- Markdown 文件预览 -->
            <div v-else-if="documentUrl && isMarkdownFile" class="markdown-preview">
              <div v-if="markdownContent" v-html="markdownContent" class="markdown-content"></div>
              <div v-else-if="markdownError" class="markdown-fallback">
                <p>Markdown 文档预览失败</p>
                <p class="text-sm text-gray-500">{{ markdownError }}</p>
                <Button label="重试" icon="pi pi-refresh" @click="loadMarkdownDocument" class="mt-3" />
              </div>
            </div>

            <!-- 思维导图预览 -->
            <div v-else-if="documentUrl && isMindMapFile" class="mindmap-preview">
              <div ref="mindmapContainer" style="height: 100%; width: 100%;"></div>
              <div v-if="mindmapError" class="mindmap-fallback">
                <p>思维导图预览失败</p>
                <p class="text-sm text-gray-500">{{ mindmapError }}</p>
                <Button label="重试" icon="pi pi-refresh" @click="loadMindMapDocument" class="mt-3" />
              </div>
            </div>

            <!-- PPT 文件预览 -->
            <div v-else-if="documentUrl && isPptFile" class="ppt-preview" ref="pptContainer">
              <canvas ref="pptCanvas" style="max-width: 100%; max-height: 100%;"></canvas>
              <div v-if="pptError" class="ppt-fallback">
                <p>PPT 预览失败</p>
                <p class="text-sm text-gray-500">{{ pptError }}</p>
                <Button label="重试" icon="pi pi-refresh" @click="loadPptDocument" class="mt-3" />
              </div>
            </div>

            <!-- 图片文件预览 -->
            <div v-else-if="documentUrl && isImageFile" class="image-preview">
              <img :src="documentUrl" alt="图片预览" style="max-width: 100%; max-height: 100%; object-fit: contain" />
            </div>

            <!-- 文本文件预览 -->
            <div v-else-if="documentUrl && isTextFile" class="text-content">
              <pre>{{ textContent }}</pre>
            </div>

            <!-- 其他文件类型或无法预览 -->
            <div v-else-if="documentUrl" class="unsupported-preview">
              <i class="pi pi-file" style="font-size: 3rem; color: #6c757d"></i>
              <p>此文件类型暂不支持在线预览</p>
              <p class="text-sm text-gray-500">文件类型: {{ document?.type || 'unknown' }}</p>
              <Button label="下载查看" icon="pi pi-download" @click="downloadDocument" class="mt-3" />
            </div>

            <!-- 加载中或无文档 -->
            <div v-else class="no-preview">
              <i class="pi pi-file" style="font-size: 3rem; color: #6c757d"></i>
              <p>文档预览</p>
              <p class="text-sm text-gray-500">正在加载文档内容...</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <Button v-if="documentUrl" label="在新窗口中打开" icon="pi pi-external-link" @click="openInNewWindow" class="p-button-outlined mr-2" />
        <Button v-if="documentUrl" label="下载" icon="pi pi-download" @click="downloadDocument" class="p-button-outlined mr-2" />
        <Button label="关闭" icon="pi pi-times" @click="handleClose" />
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue';
import { useToast } from 'primevue/usetoast';
import Dialog from 'primevue/dialog';
import Button from 'primevue/button';
import ProgressSpinner from 'primevue/progressspinner';
import Tag from 'primevue/tag';

// 文档预览相关库
import * as pdfjsLib from 'pdfjs-dist';
import { renderAsync } from 'docx-preview';
import { marked } from 'marked';
import VueOfficeExcel from '@vue-office/excel';
import jsMind from 'jsmind';

import { KnowledgeStrategyFactory } from '@/service/knowledge/KnowledgeStrategyFactory';
import { formatFileSize, formatDateLocale } from '@/utils/helpers';

// 配置 PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;

// 响应式数据
const visible = ref(false);
const loading = ref(false);
const error = ref('');
const document = ref<any>(null);
const documentUrl = ref('');
const textContent = ref('');

// 新增的响应式数据
const wordError = ref('');
const excelError = ref('');
const markdownError = ref('');
const mindmapError = ref('');
const pptError = ref('');
const markdownContent = ref('');

// DOM 引用
const wordContainer = ref<HTMLElement>();
const mindmapContainer = ref<HTMLElement>();
const pptContainer = ref<HTMLElement>();
const pptCanvas = ref<HTMLCanvasElement>();

// Toast
const toast = useToast();

// 知识库服务
const knowledgeService = KnowledgeStrategyFactory.createStrategy();

// 文件类型常量
const IMAGE_EXTENSIONS = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
const TEXT_EXTENSIONS = ['txt', 'json', 'xml', 'csv', 'log', 'js', 'ts', 'css', 'html', 'htm'];
const WORD_EXTENSIONS = ['docx', 'doc'];
const EXCEL_EXTENSIONS = ['xlsx', 'xls'];
const MARKDOWN_EXTENSIONS = ['md', 'markdown'];
const MINDMAP_EXTENSIONS = ['xmind', 'mm'];
const PPT_EXTENSIONS = ['ppt', 'pptx'];

// 文件类型检测函数
function getFileExtension(fileName: string): string {
  return fileName.toLowerCase().split('.').pop() || '';
}

// 计算属性 - 根据文件名后缀判断文件类型
const isPdfFile = computed(() => {
  const fileName = document.value?.title || document.value?.name || '';
  return getFileExtension(fileName) === 'pdf';
});

const isImageFile = computed(() => {
  const fileName = document.value?.title || document.value?.name || '';
  const ext = getFileExtension(fileName);
  return IMAGE_EXTENSIONS.includes(ext);
});

const isTextFile = computed(() => {
  const fileName = document.value?.title || document.value?.name || '';
  const ext = getFileExtension(fileName);
  return TEXT_EXTENSIONS.includes(ext);
});

const isWordFile = computed(() => {
  const fileName = document.value?.title || document.value?.name || '';
  const ext = getFileExtension(fileName);
  return WORD_EXTENSIONS.includes(ext);
});

const isExcelFile = computed(() => {
  const fileName = document.value?.title || document.value?.name || '';
  const ext = getFileExtension(fileName);
  return EXCEL_EXTENSIONS.includes(ext);
});

const isMarkdownFile = computed(() => {
  const fileName = document.value?.title || document.value?.name || '';
  const ext = getFileExtension(fileName);
  return MARKDOWN_EXTENSIONS.includes(ext);
});

const isMindMapFile = computed(() => {
  const fileName = document.value?.title || document.value?.name || '';
  const ext = getFileExtension(fileName);
  return MINDMAP_EXTENSIONS.includes(ext);
});

const isPptFile = computed(() => {
  const fileName = document.value?.title || document.value?.name || '';
  const ext = getFileExtension(fileName);
  return PPT_EXTENSIONS.includes(ext);
});

// 显示文档预览
async function show(doc: any) {
  if (!doc) {
    error.value = '文档数据不可用';
    return;
  }

  // 显示对话框
  visible.value = true;
  loading.value = true;
  error.value = '';

  try {
    // 设置基本文档信息
    document.value = {
      ...doc,
      metadata: doc.metadata || {},
    };

    // 如果文档有datasetId，设置到知识库服务
    if (doc.datasetId) {
      knowledgeService.setDatasetId(doc.datasetId);
    }

    // 使用knowledgeService获取文档的实际内容
    const documentData = await knowledgeService.getDocument(doc.id);

    // 更新文档数据，包含从API获取的blob和blobUrl
    document.value = {
      ...document.value,
      // type: documentData.type,
      size: documentData.size,
      metadata: {
        ...document.value.metadata,
        blobUrl: documentData.blobUrl,
        originalData: documentData.blob,
        contentType: documentData.type || 'application/octet-stream',
      },
    };

    // 根据文件类型处理内容
    const fileName = doc.title || doc.name || '';
    const ext = getFileExtension(fileName);

    // 处理文本文件
    if (documentData.blob && TEXT_EXTENSIONS.includes(ext)) {
      try {
        const text = await documentData.blob.text();
        textContent.value = text;
      } catch (textError) {
        console.warn('读取文本内容失败:', textError);
      }
    }

    // 处理Markdown文件
    if (documentData.blob && MARKDOWN_EXTENSIONS.includes(ext)) {
      try {
        const text = await documentData.blob.text();
        await loadMarkdownContent(text);
      } catch (markdownError) {
        console.warn('读取Markdown内容失败:', markdownError);
      }
    }

    // 生成文档URL
    generateDocumentUrl();

    // 根据文件类型加载预览
    await loadDocumentPreview();
  } catch (err) {
    console.error('获取文档内容失败:', err);
    error.value = err instanceof Error ? err.message : '获取文档内容失败';

    toast.add({
      severity: 'error',
      summary: '预览失败',
      detail: error.value,
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
}

// 隐藏文档预览
function hide() {
  visible.value = false;
  cleanupBlobUrl();
  document.value = null;
  documentUrl.value = '';
  textContent.value = '';
  error.value = '';

  // 清理新增的状态
  wordError.value = '';
  excelError.value = '';
  markdownError.value = '';
  mindmapError.value = '';
  pptError.value = '';
  markdownContent.value = '';
}

// 生成文档URL
function generateDocumentUrl() {
  if (!document.value) return;

  // 如果文档元数据中有blobUrl，使用它（从API获取的数据）
  if (document.value.metadata?.blobUrl) {
    documentUrl.value = document.value.metadata.blobUrl;
    return;
  }

  // 对于有内容的文档，不需要URL
  if (document.value.content) {
    return;
  }
}

// 根据文件类型加载预览
async function loadDocumentPreview() {
  if (!document.value) return;

  try {
    if (isWordFile.value) {
      await loadWordDocument();
    } else if (isPptFile.value) {
      await loadPptDocument();
    }
    // PDF使用原来的object标签方式，Excel和思维导图由组件自己处理，不需要额外加载
  } catch (err) {
    console.error('加载文档预览失败:', err);
  }
}



// 加载Word文档
async function loadWordDocument() {
  if (!document.value?.metadata?.originalData || !wordContainer.value) return;

  try {
    wordError.value = '';
    await renderAsync(document.value.metadata.originalData, wordContainer.value);
  } catch (err) {
    console.error('Word文档加载失败:', err);
    wordError.value = err instanceof Error ? err.message : 'Word文档加载失败';
  }
}

// 加载Markdown内容
async function loadMarkdownContent(text: string) {
  try {
    markdownError.value = '';
    markdownContent.value = await marked(text);
  } catch (err) {
    console.error('Markdown解析失败:', err);
    markdownError.value = err instanceof Error ? err.message : 'Markdown解析失败';
  }
}

// 重新加载Markdown文档
async function loadMarkdownDocument() {
  if (!document.value?.metadata?.originalData) return;

  try {
    const text = await new Blob([document.value.metadata.originalData]).text();
    await loadMarkdownContent(text);
  } catch (err) {
    console.error('Markdown文档加载失败:', err);
    markdownError.value = err instanceof Error ? err.message : 'Markdown文档加载失败';
  }
}

// 加载Excel文档（重试函数）
function loadExcelDocument() {
  excelError.value = '';
  // Excel组件会自动重新加载
}

// 处理Excel错误
function handleExcelError(error: any) {
  console.error('Excel预览失败:', error);
  excelError.value = error?.message || 'Excel预览失败';
}

// 加载思维导图文档
async function loadMindMapDocument() {
  if (!document.value?.metadata?.originalData || !mindmapContainer.value) return;

  try {
    mindmapError.value = '';
    // 这里需要根据实际的思维导图文件格式来处理
    // jsMind通常需要JSON格式的数据
    console.warn('思维导图预览功能需要根据具体文件格式实现');
    mindmapError.value = '思维导图预览功能暂未完全实现';
  } catch (err) {
    console.error('思维导图加载失败:', err);
    mindmapError.value = err instanceof Error ? err.message : '思维导图加载失败';
  }
}

// 加载PPT文档
async function loadPptDocument() {
  if (!document.value?.metadata?.originalData || !pptCanvas.value) return;

  try {
    pptError.value = '';
    // 注意：pdfjs-dist主要用于PDF文件，对于PPT文件需要特殊处理
    // 这里我们尝试使用pdfjs-dist来处理PPT文件，但实际上可能需要其他库
    console.warn('PPT预览功能：pdfjs-dist主要用于PDF，PPT支持可能有限');

    // 尝试使用pdfjs-dist处理PPT文件
    const loadingTask = pdfjsLib.getDocument({ data: document.value.metadata.originalData });
    const pdf = await loadingTask.promise;
    const page = await pdf.getPage(1);

    const canvas = pptCanvas.value;
    const context = canvas.getContext('2d');
    if (!context) return;

    const viewport = page.getViewport({ scale: 1.5 });
    canvas.height = viewport.height;
    canvas.width = viewport.width;

    await page.render({
      canvasContext: context,
      viewport: viewport
    }).promise;
  } catch (err) {
    console.error('PPT加载失败:', err);
    pptError.value = err instanceof Error ? err.message : 'PPT加载失败，pdfjs-dist可能不支持此PPT格式';
  }
}

// 下载文档
function downloadDocument() {
  if (!document.value) {
    toast.add({
      severity: 'warn',
      summary: '无法下载',
      detail: '文档数据不可用',
      life: 3000,
    });
    return;
  }

  // 优先使用原始数据进行下载
  if (document.value.metadata?.originalData) {
    const blob = new Blob([document.value.metadata.originalData], {
      type: document.value.metadata.contentType || 'application/octet-stream',
    });
    const url = URL.createObjectURL(blob);

    const link = window.document.createElement('a');
    link.href = url;
    link.download = document.value.title || 'document';
    window.document.body.appendChild(link);
    link.click();
    window.document.body.removeChild(link);

    // 清理URL
    URL.revokeObjectURL(url);
  } else if (documentUrl.value) {
    // 备用方案：使用URL下载
    const link = window.document.createElement('a');
    link.href = documentUrl.value;
    link.download = document.value.title || 'document';
    link.target = '_blank';
    window.document.body.appendChild(link);
    link.click();
    window.document.body.removeChild(link);
  } else {
    toast.add({
      severity: 'warn',
      summary: '无法下载',
      detail: '文档链接不可用',
      life: 3000,
    });
  }
}

// 在新窗口中打开文档
function openInNewWindow() {
  if (!documentUrl.value) {
    toast.add({
      severity: 'warn',
      summary: '无法打开',
      detail: '文档链接不可用',
      life: 3000,
    });
    return;
  }

  window.open(documentUrl.value, '_blank');
}

// 处理visible变化
function handleVisibleChange(newVisible: boolean) {
  visible.value = newVisible;
}

// 清理Blob URL
function cleanupBlobUrl() {
  if (document.value?.metadata?.blobUrl) {
    URL.revokeObjectURL(document.value.metadata.blobUrl);
  }
}

// 关闭对话框
function handleClose() {
  hide();
}

// 暴露给父组件的方法
defineExpose({
  show,
  hide,
});
</script>

<style scoped>
.document-preview-dialog {
  --dialog-border-radius: 12px;
}

.preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.preview-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.document-info {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-item .label {
  font-weight: 600;
  color: #6c757d;
}

.info-item .value {
  color: #495057;
}

.preview-area {
  flex: 1;
  overflow: hidden; /* 防止滚动条，让内容自己处理滚动 */
  border: 1px solid #dee2e6;
  border-radius: 8px;
  background: #fff;
  min-height: 0; /* 允许 flex 子元素缩小 */
}

.document-preview {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.document-preview iframe {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.text-content {
  width: 100%;
  height: 100%;
  padding: 1rem;
  overflow: auto;
}

.text-content pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.no-preview,
.unsupported-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #6c757d;
  padding: 2rem;
  width: 100%;
  height: 100%;
}

.no-preview p,
.unsupported-preview p {
  margin: 0.5rem 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.image-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.image-preview img {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pdf-fallback,
.word-fallback,
.excel-fallback,
.markdown-fallback,
.mindmap-fallback,
.ppt-fallback {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: #6c757d;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 1rem;
}



/* Word 文档预览样式 */
.word-preview {
  width: 100%;
  height: 100%;
  overflow: auto;
  padding: 1rem;
}

/* Excel 预览样式 */
.excel-preview {
  width: 100%;
  height: 100%;
}

/* Markdown 预览样式 */
.markdown-preview {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.markdown-content {
  padding: 1rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.markdown-content h1 {
  font-size: 2rem;
  border-bottom: 2px solid #e1e4e8;
  padding-bottom: 0.3rem;
}

.markdown-content h2 {
  font-size: 1.5rem;
  border-bottom: 1px solid #e1e4e8;
  padding-bottom: 0.3rem;
}

.markdown-content h3 {
  font-size: 1.25rem;
}

.markdown-content p {
  margin-bottom: 1rem;
}

.markdown-content code {
  background: #f6f8fa;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.875rem;
}

.markdown-content pre {
  background: #f6f8fa;
  padding: 1rem;
  border-radius: 6px;
  overflow-x: auto;
  margin: 1rem 0;
}

.markdown-content pre code {
  background: none;
  padding: 0;
}

.markdown-content blockquote {
  border-left: 4px solid #dfe2e5;
  padding-left: 1rem;
  margin: 1rem 0;
  color: #6a737d;
}

.markdown-content ul,
.markdown-content ol {
  padding-left: 2rem;
  margin-bottom: 1rem;
}

.markdown-content li {
  margin-bottom: 0.25rem;
}

.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid #dfe2e5;
  padding: 0.5rem;
  text-align: left;
}

.markdown-content th {
  background: #f6f8fa;
  font-weight: 600;
}

/* 思维导图预览样式 */
.mindmap-preview {
  width: 100%;
  height: 100%;
  position: relative;
}

/* PPT 预览样式 */
.ppt-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .preview-container {
    height: calc(90vh - 180px); /* 在小屏幕上稍微调整高度 */
  }
}
</style>
